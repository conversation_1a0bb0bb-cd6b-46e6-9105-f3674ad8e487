// React core
import { useState, useEffect } from "react";
// Third-party library imports
// Services
import { ConversationStorage } from "../services/ConversationStorage";
import { audioStateManager } from "../services/AudioStateManager";
import { VoicesService } from "../services/VoicesService";
import { SpeechRecognitionService } from "../services/SpeechRecognitionService";
import { conversationStateManager } from "../services/ConversationStateManager";
import type { AudioStateData } from "../services/AudioStateManager";
import type { ConversationStateData } from "../services/ConversationStateManager";
// Components
// Utils & Constants & Helpers
import { useConversationStore } from "../stores/conversationStore";
import { GameStep, type GameStepType } from "../utils/gameUtils";
// Styles
import "./DebugPanel.scss";

interface DebugPanelProps {
  currentStep: GameStepType;
  showRulesPopup: boolean;
  aiLoading: boolean;
  generatedCharacter: string;
  gameStarted: boolean;
  initialMessage: string;
  gameWon: boolean;
}

export const DebugPanel: React.FC<DebugPanelProps> = ({
  currentStep,
  showRulesPopup,
  aiLoading,
  generatedCharacter,
  gameStarted,
  initialMessage,
  gameWon,
}) => {
  // Estados para monitorear servicios
  const [audioState, setAudioState] = useState<AudioStateData>(audioStateManager.getState());
  const [conversationManagerState, setConversationManagerState] = useState<ConversationStateData>(
    conversationStateManager.getState()
  );
  const [isExpanded, setIsExpanded] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Estados de servicios
  const conversationStore = useConversationStore();
  const conversationStorage = ConversationStorage.getInstance();
  const voicesService = VoicesService.getInstance();
  const speechService = SpeechRecognitionService.getInstance();

  // Estados adicionales para mostrar
  const [gameProgress, setGameProgress] = useState<any>(null);
  const [currentMessages, setCurrentMessages] = useState<any[]>([]);

  // Actualizar estados periódicamente
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      setAudioState(audioStateManager.getState());
      setConversationManagerState(conversationStateManager.getState());

      // Obtener progreso del juego
      const progress = conversationStorage.getGameProgress();
      setGameProgress(progress);

      // Obtener mensajes de la sesión actual
      const messages = conversationStorage.getMessages();
      setCurrentMessages(messages);
    }, 500);

    return () => clearInterval(interval);
  }, [conversationStorage, autoRefresh]);

  const formatValue = (value: any): string => {
    if (value === null) return "null";
    if (value === undefined) return "undefined";
    if (typeof value === "boolean") return value.toString();
    if (typeof value === "string") return `"${value}"`;
    if (typeof value === "object") return JSON.stringify(value, null, 2);
    return String(value);
  };

  const getStepName = (step: GameStepType): string => {
    switch (step) {
      case GameStep.COOKIE_BANNER: return "COOKIE_BANNER";
      case GameStep.WELCOME: return "WELCOME";
      case GameStep.MAIN_MENU: return "MAIN_MENU";
      case GameStep.GAME_PLAYING: return "GAME_PLAYING";
      case GameStep.GAME_END: return "GAME_END";
      default: return "UNKNOWN";
    }
  };

  const exportDebugData = () => {
    const debugData = {
      timestamp: new Date().toISOString(),
      gameState: {
        currentStep: getStepName(currentStep),
        gameStarted,
        gameWon,
        aiLoading,
        showRulesPopup,
        generatedCharacter: generatedCharacter.substring(0, 200),
        initialMessage: initialMessage.substring(0, 200),
      },
      conversationStore,
      audioState,
      conversationManagerState,
      gameProgress,
      currentMessages: currentMessages.map(msg => ({
        id: msg.id,
        role: msg.role,
        content: msg.content.substring(0, 100),
        createdAt: msg.createdAt
      })),
      services: {
        speechSupported: speechService.isSupported(),
        speechListening: speechService.isListening(),
        voiceConfigured: voicesService.isVoiceConfigured(),
      },
      environment: {
        VITE_ENV: import.meta.env.VITE_ENV,
        userAgent: navigator.userAgent,
        url: window.location.href,
      }
    };

    const dataStr = JSON.stringify(debugData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `debug-data-${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  if (import.meta.env.VITE_ENV !== "development") {
    return null;
  }

  return (
    <div className={`debug-panel ${isExpanded ? 'expanded' : 'collapsed'}`}>
      <div className="debug-header">
        <span onClick={() => setIsExpanded(!isExpanded)} style={{ cursor: 'pointer', flex: 1 }}>
          🐛 Debug Panel
        </span>
        <div className="debug-controls">
          <button
            onClick={(e) => { e.stopPropagation(); exportDebugData(); }}
            className="export-btn"
            title="Export debug data to JSON file"
          >
            💾
          </button>
          <button
            onClick={(e) => { e.stopPropagation(); setAutoRefresh(!autoRefresh); }}
            className={`refresh-btn ${autoRefresh ? 'active' : 'inactive'}`}
            title={autoRefresh ? 'Disable auto-refresh' : 'Enable auto-refresh'}
          >
            {autoRefresh ? '⏸️' : '▶️'}
          </button>
          <span className="toggle-icon" onClick={() => setIsExpanded(!isExpanded)} style={{ cursor: 'pointer' }}>
            {isExpanded ? '▼' : '▶'}
          </span>
        </div>
      </div>

      {isExpanded && (
        <div className="debug-content">
          {/* Game State */}
          <div className="debug-section">
            <h4>🎮 Game State</h4>
            <div className="debug-item">
              <span className="label">Current Step:</span>
              <span className="value">{getStepName(currentStep)}</span>
            </div>
            <div className="debug-item">
              <span className="label">Game Started:</span>
              <span className="value">{formatValue(gameStarted)}</span>
            </div>
            <div className="debug-item">
              <span className="label">Game Won:</span>
              <span className="value">{formatValue(gameWon)}</span>
            </div>
            <div className="debug-item">
              <span className="label">AI Loading:</span>
              <span className="value">{formatValue(aiLoading)}</span>
            </div>
            <div className="debug-item">
              <span className="label">Show Rules Popup:</span>
              <span className="value">{formatValue(showRulesPopup)}</span>
            </div>
            <div className="debug-item">
              <span className="label">Generated Character:</span>
              <span className="value">{formatValue(generatedCharacter.substring(0, 100) + (generatedCharacter.length > 100 ? "..." : ""))}</span>
            </div>
            <div className="debug-item">
              <span className="label">Initial Message:</span>
              <span className="value">{formatValue(initialMessage.substring(0, 100) + (initialMessage.length > 100 ? "..." : ""))}</span>
            </div>
          </div>

          {/* Conversation Store */}
          <div className="debug-section">
            <h4>💬 Conversation Store</h4>
            <div className="debug-item">
              <span className="label">Conversation State:</span>
              <span className="value">{formatValue(conversationStore.conversationState)}</span>
            </div>
            <div className="debug-item">
              <span className="label">Is Active:</span>
              <span className="value">{formatValue(conversationStore.isActive)}</span>
            </div>
            <div className="debug-item">
              <span className="label">Is Listening:</span>
              <span className="value">{formatValue(conversationStore.isListening)}</span>
            </div>
            <div className="debug-item">
              <span className="label">Smart Microphone:</span>
              <span className="value">{formatValue(conversationStore.smartMicrophoneEnabled)}</span>
            </div>
            <div className="debug-item">
              <span className="label">Is Audio Playing:</span>
              <span className="value">{formatValue(conversationStore.isAudioPlaying)}</span>
            </div>
            <div className="debug-item">
              <span className="label">Error:</span>
              <span className="value">{formatValue(conversationStore.error)}</span>
            </div>
          </div>

          {/* Audio State */}
          <div className="debug-section">
            <h4>🔊 Audio State</h4>
            <div className="debug-item">
              <span className="label">Audio State:</span>
              <span className="value">{formatValue(audioState.state)}</span>
            </div>
            <div className="debug-item">
              <span className="label">Current Audio:</span>
              <span className="value">{formatValue(audioState.currentAudio !== null)}</span>
            </div>
            <div className="debug-item">
              <span className="label">Duration:</span>
              <span className="value">{formatValue(audioState.duration)}</span>
            </div>
            <div className="debug-item">
              <span className="label">Current Time:</span>
              <span className="value">{formatValue(audioState.currentTime)}</span>
            </div>
            <div className="debug-item">
              <span className="label">Background Music Playing:</span>
              <span className="value">{formatValue(audioState.isBackgroundMusicPlaying)}</span>
            </div>
            <div className="debug-item">
              <span className="label">Audio Error:</span>
              <span className="value">{formatValue(audioState.error)}</span>
            </div>
          </div>

          {/* Conversation Manager State */}
          <div className="debug-section">
            <h4>🎯 Conversation Manager</h4>
            <div className="debug-item">
              <span className="label">State:</span>
              <span className="value">{formatValue(conversationManagerState.state)}</span>
            </div>
            <div className="debug-item">
              <span className="label">Is Active:</span>
              <span className="value">{formatValue(conversationManagerState.isActive)}</span>
            </div>
            <div className="debug-item">
              <span className="label">Is Listening:</span>
              <span className="value">{formatValue(conversationManagerState.isListening)}</span>
            </div>
            <div className="debug-item">
              <span className="label">Is Audio Playing:</span>
              <span className="value">{formatValue(conversationManagerState.isAudioPlaying)}</span>
            </div>
            <div className="debug-item">
              <span className="label">Can Auto Activate:</span>
              <span className="value">{formatValue(conversationManagerState.canAutoActivate)}</span>
            </div>
          </div>

          {/* Services State */}
          <div className="debug-section">
            <h4>⚙️ Services State</h4>
            <div className="debug-item">
              <span className="label">Speech Recognition Supported:</span>
              <span className="value">{formatValue(speechService.isSupported())}</span>
            </div>
            <div className="debug-item">
              <span className="label">Speech Recognition Listening:</span>
              <span className="value">{formatValue(speechService.isListening())}</span>
            </div>
            <div className="debug-item">
              <span className="label">Voice Service Configured:</span>
              <span className="value">{formatValue(voicesService.isVoiceConfigured())}</span>
            </div>
          </div>

          {/* Game Progress */}
          {gameProgress && (
            <div className="debug-section">
              <h4>📊 Game Progress</h4>
              <div className="debug-item">
                <span className="label">Game Finished:</span>
                <span className="value">{formatValue(gameProgress.gameFinished)}</span>
              </div>
              <div className="debug-item">
                <span className="label">Game Won:</span>
                <span className="value">{formatValue(gameProgress.gameWon)}</span>
              </div>
              <div className="debug-item">
                <span className="label">Attempts:</span>
                <span className="value">{formatValue(gameProgress.attempts)}</span>
              </div>
              <div className="debug-item">
                <span className="label">Hints Count:</span>
                <span className="value">{formatValue(gameProgress.hints?.length || 0)}</span>
              </div>
            </div>
          )}

          {/* Environment */}
          <div className="debug-section">
            <h4>🌍 Environment</h4>
            <div className="debug-item">
              <span className="label">VITE_ENV:</span>
              <span className="value">{formatValue(import.meta.env.VITE_ENV)}</span>
            </div>
            <div className="debug-item">
              <span className="label">Online:</span>
              <span className="value">{formatValue(navigator.onLine)}</span>
            </div>
            <div className="debug-item">
              <span className="label">Language:</span>
              <span className="value">{formatValue(navigator.language)}</span>
            </div>
            <div className="debug-item">
              <span className="label">Platform:</span>
              <span className="value">{formatValue((navigator as any).userAgentData?.platform || "Browser")}</span>
            </div>
            <div className="debug-item">
              <span className="label">Screen Size:</span>
              <span className="value">{formatValue(`${window.screen.width}x${window.screen.height}`)}</span>
            </div>
            <div className="debug-item">
              <span className="label">Viewport Size:</span>
              <span className="value">{formatValue(`${window.innerWidth}x${window.innerHeight}`)}</span>
            </div>
            <div className="debug-item">
              <span className="label">User Agent:</span>
              <span className="value">{formatValue(navigator.userAgent.substring(0, 50) + "...")}</span>
            </div>
            <div className="debug-item">
              <span className="label">Timestamp:</span>
              <span className="value">{formatValue(new Date().toLocaleTimeString())}</span>
            </div>
          </div>

          {/* Current Session Messages */}
          <div className="debug-section">
            <h4>📝 Current Session</h4>
            <div className="debug-item">
              <span className="label">Current Session:</span>
              <span className="value">{formatValue(conversationStorage.getCurrentSession()?.id || "None")}</span>
            </div>
            <div className="debug-item">
              <span className="label">Messages Count:</span>
              <span className="value">{formatValue(currentMessages.length)}</span>
            </div>
            <div className="debug-item">
              <span className="label">Last Message:</span>
              <span className="value">{formatValue(currentMessages[currentMessages.length - 1]?.content?.substring(0, 50) + "..." || "None")}</span>
            </div>
          </div>

          {/* Storage & Performance */}
          <div className="debug-section">
            <h4>💾 Storage & Performance</h4>
            <div className="debug-item">
              <span className="label">LocalStorage Used:</span>
              <span className="value">{formatValue(JSON.stringify(localStorage).length + " chars")}</span>
            </div>
            <div className="debug-item">
              <span className="label">Memory (approx):</span>
              <span className="value">{formatValue((performance as any).memory ? `${Math.round((performance as any).memory.usedJSHeapSize / 1024 / 1024)}MB` : "N/A")}</span>
            </div>
            <div className="debug-item">
              <span className="label">Connection:</span>
              <span className="value">{formatValue((navigator as any).connection?.effectiveType || "Unknown")}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
